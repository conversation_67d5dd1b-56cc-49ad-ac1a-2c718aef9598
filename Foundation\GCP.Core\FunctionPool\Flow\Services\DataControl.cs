using GCP.Common;
using GCP.FunctionPool.Flow.Models;
using GCP.Functions.Common;
using Jint;
using System.Collections;
using System.Text;

namespace GCP.FunctionPool.Flow.Services
{
    class DataControl : DataBaseService
    {

        [Function("dataForEach", "迭代字典/数组")]
        public object ForEachData(DataForEach data)
        {
            var listObj = GetDataValue(data.DataSource);
            var sourceList = new ArrayList();

            if (listObj is not ICollection list)
            {
                sourceList.Add(listObj);
            }
            else
            {
                sourceList = new ArrayList(list);
            }

            return sourceList;
        }

        [Function("controlLoop", "循环")]
        public void ControlLoop()
        {
            
        }

        [Function("dataBranch", "如果")]
        public bool BranchData(DataBranch data)
        {
            var engine = this.GetEngine();
            var result = false;
            if(data.Type == "script")
            {
                var jsValue = engine.Evaluate(data.Script);
                result = jsValue.AsBoolean();
            }
            else
            {
                result = FlowUtils.GetConditionValue(data.Conditions, engine: engine, context: this.Context);
            }

            return result;
        }

        [Function("controlBreak", "中断迭代")]
        public void ControlBreak()
        {
            this.Context.BreakCancellationTokenSource?.Cancel();
            this.Context.BreakCancellationTokenSource?.Token.ThrowIfCancellationRequested();
        }

        [Function("controlContinue", "跳过当前迭代")]
        public void ControlContinue()
        {
            this.Context.ContinueCancellationTokenSource?.Cancel();
            this.Context.ContinueCancellationTokenSource?.Token.ThrowIfCancellationRequested();
        }

        [Function("runScript", "运行脚本")]
        public object RunScript(DataScript data)
        {
            var engine = this.GetEngine();
            var jsValue = engine.Evaluate(data.Script);
            return jsValue.ToObject();
        }

        [Function("dataTransaction", "数据库事务")]
        public void TransactionData()
        {

        }

        [Function("controlTryCatch", "异常捕获")]
        public void TryCatch()
        {

        }

        [Function("controlDelay", "延迟")]
        public async Task Delay(DataDelay data)
        {
            var engine = FlowUtils.GetEngine(Context);

            var millisecondsDelay = data.MillisecondsDelay.GetDataValue<int?>(engine, globalData: Context.globalData);
            if (millisecondsDelay == null)
            {
                throw new CustomException("延迟时间不能为空");
            }
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine(millisecondsDelay.Value.ToString());
            }
            await Task.Delay(millisecondsDelay.Value);
        }

        [Function("throwError", "抛出异常")]
        public async Task ThrowError(DataThrowError data)
        {
            var errorMessageObj = GetDataValue(data.ErrorMessage);
            var errorMessage = Convert.ToString(errorMessageObj);
            await this.Context.SqlLog.Warn($"{data.Name}：{errorMessage}", true);
            throw new CustomSkipException(errorMessage);
        }
    }
}
