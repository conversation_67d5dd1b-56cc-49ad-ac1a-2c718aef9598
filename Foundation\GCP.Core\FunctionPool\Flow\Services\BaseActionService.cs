using GCP.Common;
using GCP.Functions.Common;
using System.Collections.Generic;

namespace GCP.FunctionPool.Flow.Services
{
    /// <summary>
    /// 动作服务基类
    /// 提供统一的ROOT节点输入参数处理
    /// </summary>
    public abstract class BaseActionService : DataBaseService
    {
        /// <summary>
        /// 处理输入参数中的ROOT节点
        /// 只在输入参数处理时调用，避免中间过程的性能损失
        /// </summary>
        /// <param name="inputFlowData">输入参数FlowData配置</param>
        /// <returns>处理后的输入数据</returns>
        protected Dictionary<string, object> ProcessInputParameters(List<FlowData> inputFlowData)
        {
            if (inputFlowData == null || inputFlowData.Count == 0)
            {
                return new Dictionary<string, object>();
            }

            var inputDic = new Dictionary<string, object>();
            var engine = this.GetEngine();
            
            // 使用FlowUtils绑定输入参数
            FlowUtils.BindResult(inputFlowData, inputDic, engine, this.Context);

            // 应用ROOT节点处理
            var processedInput = RootNodeHelper.ProcessInputParameters(inputDic, inputFlowData);
            
            // 如果处理后的结果不是字典，需要包装
            if (processedInput is Dictionary<string, object> resultDic)
            {
                return resultDic;
            }
            else
            {
                // 如果ROOT节点处理后返回非字典类型，包装成字典
                return new Dictionary<string, object> { { "data", processedInput } };
            }
        }


    }
}
